.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
  overflow: hidden;
}

.confetti-particle {
  position: absolute;
  top: -10px;
  animation: confetti-fall linear forwards;
  pointer-events: none;
}

/* Circle confetti */
.confetti-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

/* Square confetti */
.confetti-square {
  width: 8px;
  height: 8px;
}

/* Triangle confetti */
.confetti-triangle {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 8px solid var(--particle-color);
  background-color: transparent !important;
}

/* Confetti falling animation */
@keyframes confetti-fall {
  0% {
    transform: translateY(-10px) rotateZ(0deg);
    opacity: 1;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotateZ(720deg);
    opacity: 0;
  }
}

/* Alternative animation for some particles */
.confetti-particle:nth-child(2n) {
  animation-name: confetti-fall-wobble;
}

@keyframes confetti-fall-wobble {
  0% {
    transform: translateY(-10px) rotateZ(0deg) translateX(0px);
    opacity: 1;
  }
  10% {
    transform: translateY(10vh) rotateZ(72deg) translateX(5px);
    opacity: 1;
  }
  20% {
    transform: translateY(20vh) rotateZ(144deg) translateX(-5px);
  }
  30% {
    transform: translateY(30vh) rotateZ(216deg) translateX(5px);
  }
  40% {
    transform: translateY(40vh) rotateZ(288deg) translateX(-5px);
  }
  50% {
    transform: translateY(50vh) rotateZ(360deg) translateX(5px);
  }
  60% {
    transform: translateY(60vh) rotateZ(432deg) translateX(-5px);
  }
  70% {
    transform: translateY(70vh) rotateZ(504deg) translateX(5px);
  }
  80% {
    transform: translateY(80vh) rotateZ(576deg) translateX(-5px);
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotateZ(720deg) translateX(0px);
    opacity: 0;
  }
}

/* Floating animation for some particles */
.confetti-particle:nth-child(3n) {
  animation-name: confetti-fall-float;
}

@keyframes confetti-fall-float {
  0% {
    transform: translateY(-10px) rotateZ(0deg) translateX(0px);
    opacity: 1;
  }
  25% {
    transform: translateY(25vh) rotateZ(180deg) translateX(10px);
  }
  50% {
    transform: translateY(50vh) rotateZ(360deg) translateX(-10px);
  }
  75% {
    transform: translateY(75vh) rotateZ(540deg) translateX(10px);
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotateZ(720deg) translateX(0px);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .confetti-circle,
  .confetti-square {
    width: 6px;
    height: 6px;
  }
  
  .confetti-triangle {
    border-left-width: 3px;
    border-right-width: 3px;
    border-bottom-width: 6px;
  }
}

@media (max-width: 480px) {
  .confetti-circle,
  .confetti-square {
    width: 4px;
    height: 4px;
  }
  
  .confetti-triangle {
    border-left-width: 2px;
    border-right-width: 2px;
    border-bottom-width: 4px;
  }
}
